<template>
  <BasicModal
    v-model:open="openModal"
    title="单据再现"
    width="80%"
    @fullscreen="onFullscreen"
    :maskClosable="false"
    :defaultFullscreen="false"
    ref="modalRef"
    :style="{ backgroundColor: 'rgb(225, 242, 232)' }"
  >
    <div v-if="loading" class="flex justify-center items-center h-96">
      <a-spin />
    </div>

    <div
      v-else
      class="relative bg-white"
      ref="printRef"
      :style="{ backgroundColor: 'rgb(225 242 232)' }"
    >
      <!-- 菜单区域 -->
      <div class="nav-menu-container" @click="handleContainerClick">
        <div class="menu-group">
          <template v-for="item in djStyle?.menuInfoList?.filter((i) => i.dsp)" :key="item.lcTitle">
            <Dropdown
              v-if="item.sonMenuList && item.sonMenuList.length > 0"
              placement="bottom"
              :trigger="['click']"
              :dropMenuList="transformMenuList(item.sonMenuList)"
              :selectedKeys="selectedKeys"
              @menu-event="handleMenuEvent"
              overlayClassName="app-locale-picker-overlay"
            >
              <a-button preIcon="ant-design:windows-outlined" type="link">
                {{ item.lcTitle }}
              </a-button>
            </Dropdown>
            <a-button
              v-else
              type="link"
              preIcon="ant-design:select-outlined"
              @click="handlerToolbar(item)"
            >
              {{ item.lcTitle }}
            </a-button>
          </template>
          <a-button
            type="link"
            v-if="bizId"
            preIcon="ant-design:select-outlined"
            @click="handleWorkflowClick"
          >
            工作流
          </a-button>
        </div>
      </div>

      <!-- 表单区域 -->
      <div class="rounded-sm" :style="{ backgroundColor: 'rgb(225 242 232)' }">
        <DjForm
          :items="djStyle?.kbxTableList?.filter((item) => item.isActive && item.upMx) || []"
          :djInfo="djStyle"
          v-model="formData"
          class="header-section"
        />
      </div>

      <!-- 表格区域 -->
      <div v-if="djStyle.mxActive">
        <VxeBasicTable
          ref="tableRef"
          v-bind="{
            ...gridOptions,
            height: computedTableHeight(),
          }"
          v-on="gridEvents"
        >
          <template #toolbar_buttons>
            <div class="flex items-center justify-between w-full px-2">
              <div>{{ djStyle?.runTitle }}明细</div>
            </div>
          </template>
        </VxeBasicTable>
      </div>

      <!-- 底部汇总区域 -->
      <div class="mt-4">
        <DjFooter
          :style="{ backgroundColor: 'rgb(225 242 232)' }"
          v-if="djStyle?.kbxTableList?.filter((item) => item.isActive && !item.upMx).length > 0"
          :items="djStyle?.kbxTableList?.filter((item) => item.isActive && !item.upMx) || []"
          :djInfo="djStyle"
          :formData="formData"
          :mxData="djData?.mxdata || []"
          class="footer-section"
        />
      </div>
    </div>

    <!-- 底部工具栏 -->
    <template #footer>
      <div class="flex justify-end gap-2">
        <a-button @click="handlePrint">打印</a-button>
        <a-button @click="handleCancel">关闭</a-button>
      </div>
    </template>

    <!-- 工作流弹窗 -->
    <WorkflowModal
      v-if="showWorkflowModal"
      v-model:open="showWorkflowModal"
      :danjbh="danjbh"
      :task-id="taskId"
      :task-kind-id="taskKindId"
      :group-id="1"
      :status="''"
      :biz-id="bizId"
      :djlx="djStyle?.djlx"
      :is-show-edit-btn="''"
      @submit="handleApprovalSubmit"
      @close="showWorkflowModal = false"
    />

    <!-- 资料详情弹窗 -->
    <ZiliaoDetailsModel
      v-if="ziliaoModalOpen"
      v-model:open="ziliaoModalOpen"
      :tableHeader="ziliaoData?.tableheader || []"
      :tableValue="ziliaoData?.tablevalue || {}"
      @cancel="() => (ziliaoModalOpen = false)"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, watch, computed, onMounted, reactive } from 'vue';
  import { getDjRepresentApi } from '@/api/template';
  import { Modal, Button as AButton, Spin as ASpin } from 'ant-design-vue';
  import { BasicModal } from '@/components/Modal';
  import { VxeBasicTable } from '@/components/VxeTable';
  import { Dropdown } from '@/components/Dropdown';
  import DjForm from './DjForm.vue';
  import DjFooter from './DjFooter.vue';
  import WorkflowModal from './WorkflowModal.vue';
  import ZiliaoDetailsModel from '../../datacard/components/ZiliaoDetailsModel.vue';
  import nzhcn from 'nzh/cn';
  import { submitApproval } from '@/api/workflow';
  import { queryArchiveDataApi } from '@/api/datacard';
  import { formatNumberByFldDec } from '../../template/util';

  const props = defineProps({
    danjbh: {
      type: String,
      required: true,
    },
    open: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['update:open', 'cancel']);

  const loading = ref(false);
  const djData = ref<Record<string, any>>({});
  const djStyle = ref<any>({});
  const bizId = ref('');
  const formData = ref<Record<string, any>>({});
  const printRef = ref<HTMLElement | null>(null);
  const tableRef = ref();
  const modalRef = ref();
  const tableHeight = ref(0);
  const showWorkflowModal = ref(false);
  const taskId = ref(''); // 当前任务ID
  const taskKindId = ref(''); // 任务种类ID
  const selectedKeys = ref([]);

  // 资料详情相关
  const ziliaoModalOpen = ref(false);
  const ziliaoData = ref<any>(null);

  // 表格配置
  const gridOptions = reactive({
    border: true,
    showOverflow: true,
    showHeaderOverflow: true,
    size: 'mini',
    stripe: true,
    rowConfig: { height: 40 },
    columnConfig: {
      resizable: true,
    },
    headerConfig: {
      height: 40,
    },
    toolbarConfig: {
      custom: false,
      export: false,
      print: false,
      zoom: false,
      refresh: false,
      slots: {
        buttons: 'toolbar_buttons',
      },
    },
    columns: [],
    // 分页配置
    pagerConfig: {
      enabled: true,
      pageSize: 50,
      pageSizes: [20, 50, 100, 200],
    },
    // 数据代理配置
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          try {
            const tableData = djData.value.mxdata || [];
            const startIndex = (page.currentPage - 1) * page.pageSize;
            const endIndex = startIndex + page.pageSize;

            // 计算表格数据的同时更新底部汇总数据
            calculateFooterData(tableData);
            return {
              items: tableData.slice(startIndex, endIndex),
              total: tableData.length,
            };
          } catch (error) {
            console.error('加载表格数据失败:', error);
            return {
              items: [],
              total: 0,
            };
          }
        },
      },
    },
  });

  const openModal = computed({
    get: () => props.open,
    set: (val) => emit('update:open', val),
  });

  function computedTableHeight() {
    const totalHeight = window.innerHeight;
    const formHeight = (djStyle.value?.tdHeight ?? 0) + 16;
    const footerHeight = djStyle.value?.qtHeight || 50;
    const availableHeight = (totalHeight - formHeight - footerHeight - 40) * 0.8;
    return Math.max(availableHeight, 400);
  }

  // 计算底部汇总数据
  function calculateFooterData(tableData: any[]) {
    if (!tableData?.length) return;
    const footerItems =
      djStyle.value?.kbxTableList?.filter((item) => !item.upMx && item.isActive) || [];
    footerItems.forEach((item) => {
      if (item.lx === 'S' || item.lx === 'P') {
        // 数字汇总
        const sum = tableData.reduce((prev, cur) => {
          const value = cur[item.fieldName];
          if (value === undefined || value === null || value === '') return prev;
          return prev + Number(value);
        }, 0);

        // 更新表单数据，应用字段的 fldDec 精度配置
        if (item.lx === 'P') {
          // 中文大写金额
          formData.value[item.fieldName] = nzhcn.encodeB(sum);
        } else {
          // 数字金额，使用 fldDec 配置格式化显示精度
          formData.value[item.fieldName] = formatNumberByFldDec(sum, item.fldDec);
        }
      }
    });
  }

  // 处理表格行点击事件
  function handleCellClick({ row }: { row: any }) {
    console.log('点击明细行数据:', row);

    if (!row || !djStyle.value?.kbxTableList) return;

    // 获取所有非明细项（表单头部和底部字段）
    const nonDetailItems = djStyle.value.kbxTableList.filter((item) => item.isActive) || [];

    // 遍历非明细项，查找与明细行数据中字段名相同的字段
    nonDetailItems.forEach((item) => {
      const fieldName = item.fieldName;

      // 如果明细行数据中存在该字段，则同步到表单数据中
      if (
        Object.prototype.hasOwnProperty.call(row, fieldName) &&
        row[fieldName] !== undefined &&
        row[fieldName] !== null
      ) {
        console.log(`同步字段 ${fieldName}: ${row[fieldName]} -> 表单数据`);

        // 根据字段类型进行适当的数据转换
        let syncValue = row[fieldName];

        // 如果是数字类型且字段类型为P（大写金额），则转换为大写
        if (item.lx === 'P' && typeof syncValue === 'number') {
          syncValue = nzhcn.encodeB(syncValue);
        }

        // 更新表单数据
        formData.value[fieldName] = syncValue;
      }
    });

    console.log('同步后的表单数据:', formData.value);
  }

  // 表格事件配置
  const gridEvents = {
    cellClick: handleCellClick,
  };

  // 组件挂载时加载数据
  onMounted(async () => {
    if (props.danjbh && props.open) {
      await loadDjRepresentData(props.danjbh);
      setTimeout(() => {
        tableHeight.value = computedTableHeight();
      }, 100);
    }
  });

  // 监听danjbh变化,加载数据
  watch(
    () => props.danjbh,
    async (newVal) => {
      if (newVal && openModal.value) {
        await loadDjRepresentData(newVal);
      }
    },
  );

  // 监听open变化,加载数据
  watch(
    () => openModal.value,
    async (newVal) => {
      if (newVal && props.danjbh) {
        await loadDjRepresentData(props.danjbh);
      }
    },
  );

  // 加载单据再现数据
  async function loadDjRepresentData(danjbh: string) {
    if (!danjbh) {
      Modal.warning({
        title: '参数错误',
        content: '单据编号不能为空',
        okText: '确定',
      });
      return;
    }

    try {
      loading.value = true;
      const res = await getDjRepresentApi(danjbh);

      console.log('API完整响应:', res);
      console.log('API返回数据结构:', res?.data);

      // 正确处理API返回的数据结构
      const apiData = res?.data?.data || res?.data || res || {};
      console.log('处理后的API数据:', apiData);

      // 设置表单数据
      djStyle.value = apiData?.djStyle || {};
      bizId.value = apiData?.bizId || '';
      djData.value = apiData?.djData || {};
      formData.value = djData.value?.fmxdata || {};

      // 确保数据被正确设置
      console.log('渲染数据状态：', {
        apiData有效: !!apiData,
        djStyle有效: !!djStyle.value,
        djData有效: !!djData.value,
        formData有效: !!formData.value,
        kbxTableList长度: djStyle.value?.kbxTableList?.length || 0,
        mxActive: djStyle.value?.mxActive,
        实际djStyle: djStyle.value,
      });

      // 检查表单项
      const formItemsHeader = djStyle.value.kbxTableList?.filter((item) => item.isActive) || [];
      console.log('有效表单项数量:', formItemsHeader.length);
      console.log('表单数据:', formData.value);

      // 设置表格列
      gridOptions.columns =
        djStyle.value.mxTableList
          ?.filter((item) => item.isActive)
          ?.map((item) => ({
            field: item.fieldName,
            title: item.fieldTitle,
            width: item.showLength,
            align: getColumnAlign(item.alignStyle),
            showOverflow: true,
            showHeaderOverflow: true,
          })) || [];

      // 刷新表格数据
      if (tableRef.value) {
        await tableRef.value.commitProxy('query');
      }
    } catch (error: any) {
      Modal.warn({
        title: '加载失败',
        content: error.message || '加载单据再现数据失败',
        okText: '确定',
      });
    } finally {
      loading.value = false;
    }
  }

  // 处理取消
  function handleCancel() {
    openModal.value = false;
    emit('cancel');
  }

  // 处理打印
  function handlePrint() {
    // 实现打印功能
  }

  // 获取列对齐方式
  function getColumnAlign(alignStyle: string) {
    const alignMap = {
      ALLEFT: 'left',
      ALCENTER: 'center',
      ALRIGHT: 'right',
    };
    return alignMap[alignStyle] || 'left';
  }

  // 全屏时重新计算表格
  const onFullscreen = () => {
    if (tableRef.value) {
      tableRef.value.refreshColumn();
    }
  };

  // 转换菜单列表，添加disabled属性
  function transformMenuList(menuList: any[]) {
    return menuList
      .filter((item) => item.dsp)
      .map((item) => ({
        text: item.lcTitle,
        event: item.functionName || item.lcTitle,
        disabled: false, // 启用菜单项
      }));
  }

  function handleContainerClick() {
    // 空函数，仅用于保持与render.vue的结构一致
  }

  function handleMenuEvent(menu: string) {
    console.log('菜单事件:', menu);

    // 根据不同的菜单事件执行不同的操作
    let menuLower = menu.toLowerCase();
    switch (menuLower) {
      case 'djpreview': // 通用打印预览
        handlePrint();
        break;
      default:
        Modal.warn({
          title: '菜单事件',
          content: `功能 "${menu}" 在单据再现页面暂不可用`,
        });
        break;
    }
  }

  function handlerToolbar(item: any) {
    console.log('工具栏按钮点击:', item);
    let functionName = item.functionName;

    if (functionName) {
      // 根据不同的功能名称执行不同的操作
      functionName = functionName.toLowerCase();
      switch (functionName) {
        case 'getdspinfo': // 商品详情
          handleShowProductDetail();
          break;
        case 'getddwinfo': // 单位详情
          handleShowUnitDetail();
          break;
        default:
          Modal.warn({
            title: '菜单功能',
            content: `功能 "${functionName}" 在单据再现页面暂不可用`,
          });
          break;
      }
    }
  }

  // 显示商品详情
  async function handleShowProductDetail() {
    // 获取当前选中行
    const currentRow = tableRef.value?.getCurrentRecord();

    if (!currentRow || !currentRow.dspid) {
      Modal.warn({
        title: '查询错误',
        content: '未找到商品编号，请先选择商品',
        okText: '确定',
      });
      return;
    }

    try {
      // 调用档案资料查询接口
      const response = await queryArchiveDataApi({ dspid: currentRow.dspid });
      // 保存数据
      ziliaoData.value = response;
      // 打开详情弹窗
      ziliaoModalOpen.value = true;
    } catch (error) {
      console.error('商品档案查询失败:', error);
      Modal.warn({
        title: '查询错误',
        content: '商品档案查询失败，请稍后重试',
        okText: '确定',
      });
    }
  }

  // 显示单位详情
  async function handleShowUnitDetail() {
    // 从非明细项(表单数据)获取ddwid字段的值
    const ddwid = formData.value.ddwid;

    if (!ddwid) {
      Modal.warn({
        title: '查询错误',
        content: '未找到单位编号，请先选择往来单位',
        okText: '确定',
      });
      return;
    }

    try {
      // 调用档案资料查询接口
      const response = await queryArchiveDataApi({ ddwid });
      // 保存数据
      ziliaoData.value = response;
      // 打开详情弹窗
      ziliaoModalOpen.value = true;
    } catch (error) {
      console.error('往来单位档案查询失败:', error);
      Modal.warn({
        title: '查询错误',
        content: '往来单位档案查询失败，请稍后重试',
        okText: '确定',
      });
    }
  }

  // 处理工作流点击事件
  async function handleWorkflowClick() {
    showWorkflowModal.value = true;
  }

  // 处理审批提交
  async function handleApprovalSubmit(data: any) {
    try {
      await submitApproval(data);
      showWorkflowModal.value = false;
      Modal.success({
        title: '提交成功',
        content: '审批已提交成功',
        okText: '确定',
      });
    } catch (error: any) {
      console.error('提交审批失败:', error);
      Modal.warn({
        title: '提交失败',
        content: '审批提交失败，请稍后重试',
        okText: '确定',
      });
    }
  }
</script>

<style lang="scss" scoped>
  :deep(.ant-modal-body) {
    background-color: rgb(225 242 232);
  }

  .nav-menu-container {
    padding: 4px 8px;
    border-bottom: 1px solid #e8e8e8;

    .menu-group {
      display: flex;
      gap: 8px;
      align-items: center;

      :deep(.ant-btn) {
        height: 28px;
        padding: 0 8px;
        color: #000;

        &[disabled] {
          border: none;
          background: transparent;
          color: rgb(0 0 0 / 45%);
          cursor: not-allowed;
        }
      }
    }
  }

  .header-section {
    flex-shrink: 0;
    border-radius: 2px;
  }

  :deep(.vxe-table--body-wrapper) {
    overflow: auto auto !important;
  }

  :deep(.vxe-table--header-wrapper) {
    overflow-x: hidden !important;
  }

  :deep(.vxe-body--row) {
    height: 40px !important;
  }

  :deep(.vxe-table) {
    border: 1px solid #000;

    .vxe-header--column {
      height: 40px !important;
      padding: 8px !important;
      border-color: #000;
      color: #000;
      font-weight: normal;
      line-height: 24px !important;
      white-space: nowrap !important;
    }

    .vxe-body--column {
      padding: 8px !important;
      border-color: #000;
    }
  }

  :deep(.vxe-grid) {
    padding: 0 !important;
  }

  :deep(.vxe-header--row) {
    height: 40px !important;
  }

  :deep(.ant-input) {
    border-radius: 0;
    border-color: transparent;

    &:focus {
      border-color: #40a9ff;
      box-shadow: none;
    }
  }

  :deep(.ant-btn) {
    border-radius: 0;
  }

  :deep(.ant-btn[disabled]) {
    border: none !important;
    background: transparent !important;
    color: rgb(0 0 0 / 45%) !important;
    cursor: not-allowed !important;
  }

  :deep(.workflow-tabs) {
    .ant-tabs-nav {
      margin-bottom: 16px;
    }

    .flow-chart-container {
      background-color: #fff;

      :deep(.node) {
        &.green {
          fill: #67c23a;
          color: #fff;
        }

        &.red {
          fill: #f56c6c;
          color: #fff;
        }

        &.white {
          stroke: #d9d9d9;
          fill: #fff;
          color: #000;
        }
      }

      :deep(.edge) {
        stroke-width: 1;
        stroke: #666;
      }

      :deep(.start),
      :deep(.end) {
        fill: #ff4500;
        color: #fff;
      }
    }
  }
</style>
